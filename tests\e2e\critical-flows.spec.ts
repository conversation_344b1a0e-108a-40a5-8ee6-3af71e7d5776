import { test, expect } from '@playwright/test'
import { createConsoleErrorMonitor } from './helpers/critical-flow-utils'

// Helper function to perform login (extracted from login-logout-flow.spec.ts)
async function login(page: any, email: string, password: string) {
  // Fill email field using role selector
  await page.getByRole('textbox', { name: 'Email' }).fill(email)

  // Fill password field using ID selector (password inputs don't have textbox role)
  await page.locator('#password').fill(password)

  // Wait for form validation to complete and button to be enabled
  await page.waitForFunction(
    () => {
      const button = document.querySelector(
        'button[type="submit"]'
      ) as HTMLButtonElement
      return button && !button.disabled
    },
    { timeout: 10000 }
  )

  // Use more specific selector and force click to bypass any potential overlay issues
  const loginButton = page.locator('button[type="submit"]')
  await loginButton.waitFor({ state: 'visible', timeout: 10000 })
  await loginButton.click({ force: true, timeout: 10000 })
}

// Helper function to wait for loading to complete
async function waitForLoadingComplete(page: any) {
  try {
    await page.waitForLoadState('networkidle', { timeout: 15000 })

    // Detect browser type for Mobile Safari special handling
    const userAgent = await page.evaluate(() => navigator.userAgent)
    const isMobileSafari =
      userAgent.includes('Safari') && userAgent.includes('Mobile')
    const loadingTimeout = isMobileSafari ? 45000 : 35000

    // Wait for specific loading text to disappear with browser-specific timeout
    await page
      .waitForFunction(
        () => {
          // Check for loading text more comprehensively
          const loadingTextElements = document.querySelectorAll('*')
          const hasLoadingText = Array.from(loadingTextElements).some(
            (el) =>
              el.textContent &&
              (el.textContent.includes('Loading exercise data') ||
                el.textContent.includes('Loading...') ||
                el.textContent.includes('Loading workout') ||
                el.textContent === 'Loading')
          )

          const loadingElements = document.querySelectorAll(
            '[data-testid*="loading"], [data-testid*="spinner"], .loading, .spinner'
          )
          const disabledLoadingButton = document.querySelector(
            'button[disabled]:has-text("Loading")'
          )
          const loadingStatus = document.querySelector(
            'status:has-text("Loading")'
          )

          return (
            !hasLoadingText &&
            loadingElements.length === 0 &&
            !disabledLoadingButton &&
            !loadingStatus
          )
        },
        { timeout: loadingTimeout }
      )
      .catch(() => {
        // For Mobile Safari, add additional fallback
        if (isMobileSafari) {
          // Mobile Safari loading timeout, applying fallback...
          return page.waitForTimeout(5000) // Give it 5 more seconds
        }
      })

    // Also wait for exercise cards to be present if we're on workout page
    if (page.url().includes('/workout')) {
      await page
        .waitForFunction(
          () => {
            const exerciseCards = document.querySelectorAll(
              '[data-testid="exercise-card"]'
            )
            return exerciseCards.length > 0
          },
          { timeout: 20000 }
        )
        .catch(() => {
          // Exercise cards not found, continuing...
        })
    }
  } catch (error) {
    // If networkidle times out, continue anyway
    // Loading state timeout, continuing...
  }
}

// Mobile-first configuration
test.use({
  viewport: { width: 375, height: 667 }, // iPhone SE
  hasTouch: true,
  isMobile: true,
})

test.describe('@critical Core User Flows - Race Condition Prevention', () => {
  // No mocks - using real API with test credentials

  test('Login flow - email/password authentication and state management', async ({
    page,
  }) => {
    // Set longer timeout for all browsers to handle dev server delays
    test.setTimeout(90000) // Increased from 60s to 90s

    // Navigate to app
    await page.goto('/')

    // Clear auth state to ensure clean test
    await page.evaluate(() => {
      window.localStorage.clear()
    })

    // Navigate directly to login page
    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    // Wait for login form to be visible with longer timeout
    await page.waitForSelector('input[type="email"]', {
      state: 'visible',
      timeout: 15000,
    })

    // Ensure page is stable before interacting
    await page.waitForLoadState('networkidle')

    // Monitor for race conditions
    const errorMonitor = createConsoleErrorMonitor(page)

    // Perform real login with test credentials
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for successful authentication
    try {
      await page.waitForURL('/program', { timeout: 10000 })
    } catch (error) {
      // If navigation fails, check current URL and auth state
      const currentUrl = page.url()
      const authState = await page.evaluate(() => {
        const authStorage = window.localStorage.getItem('drmuscle-auth')
        return authStorage ? JSON.parse(authStorage) : null
      })

      // Log debug info for timeout investigation
      // console.log('Navigation timeout - Current URL:', currentUrl)
      // console.log('Auth state:', authState)

      throw new Error(
        `Login failed. Current URL: ${currentUrl}, Auth state: ${JSON.stringify(authState)}`
      )
    }

    // Wait for auth state to be written to localStorage
    await page.waitForFunction(
      () => {
        const authStorage = window.localStorage.getItem('drmuscle-auth')
        if (!authStorage) return false
        try {
          const parsed = JSON.parse(authStorage)
          return (
            parsed?.state?.isAuthenticated === true &&
            parsed?.state?.user?.email === '<EMAIL>'
          )
        } catch {
          return false
        }
      },
      { timeout: 10000 }
    )

    // Verify no race condition errors
    expect(errorMonitor.getRaceConditionErrors()).toHaveLength(0)
  })

  test('Start workout flow - create and navigate without race conditions', async ({
    page,
  }) => {
    // Set longer timeout for stability
    test.setTimeout(90000)

    // First login and start a workout
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for page to fully load after login with extended timeout
    try {
      await page.waitForLoadState('networkidle', { timeout: 15000 })
    } catch (error) {
      // Network idle timeout, continuing...
    }

    // Check for intermediate states (Welcome back text OR success checkmark)
    const hasWelcomeText = await page
      .locator('text=Welcome back!')
      .isVisible()
      .catch(() => false)
    const hasSuccessCheckmark = await page
      .locator('img[alt*="Success"], img[alt*="checkmark"]')
      .isVisible()
      .catch(() => false)

    if (hasWelcomeText || hasSuccessCheckmark) {
      await page
        .waitForLoadState('networkidle', { timeout: 15000 })
        .catch(() => {})

      // If still on intermediate page after waiting, navigate manually
      await page.waitForTimeout(3000) // Extended pause
      if (page.url().includes('/login') || !page.url().includes('/program')) {
        await page.goto('/program')
        await page
          .waitForLoadState('networkidle', { timeout: 15000 })
          .catch(() => {})
      }
    }

    // Now wait for navigation to /program with extended timeout and retry logic
    try {
      await page.waitForURL('/program', { timeout: 30000 })
    } catch (error) {
      // If still failing, try manual navigation one more time
      await page.goto('/program')
      await page.waitForURL('/program', { timeout: 15000 })
    }

    // Monitor for race conditions during workout creation
    const errorMonitor = createConsoleErrorMonitor(page)

    // Start a new workout with correct selector based on page snapshot
    const startButton = page.getByRole('button', { name: /open workout/i })
    await expect(startButton).toBeVisible({ timeout: 15000 })

    // Wait for DOM to stabilize before clicking
    await page.waitForTimeout(1000)
    await startButton.waitFor({ state: 'attached', timeout: 10000 })

    // Use multiple attempts for click with DOM stability and extended timeout
    let clicked = false
    for (let i = 0; i < 5 && !clicked; i++) {
      try {
        await startButton.click({ force: true, timeout: 10000 }) // eslint-disable-line no-await-in-loop
        clicked = true
      } catch (error) {
        if (i === 4) throw error
        await page.waitForTimeout(2000) // eslint-disable-line no-await-in-loop
      }
    }

    // Wait for navigation to workout page with extended timeout and retry logic
    try {
      await page.waitForURL(/\/workout/, { timeout: 25000 })
    } catch (error) {
      // If navigation fails, try manual navigation
      // Navigation timeout, attempting manual navigation...
      await page.goto('/workout')
      await page.waitForURL(/\/workout/, { timeout: 15000 })
    }
    await waitForLoadingComplete(page)

    // Check for race condition errors
    expect(errorMonitor.getRaceConditionErrors()).toHaveLength(0)
  })

  test('Open exercise flow - rapid tapping without race conditions', async ({
    page,
  }) => {
    // Set longer timeout for stability
    test.setTimeout(90000)

    // First login and navigate to a workout
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for page to fully load after login with extended timeout
    try {
      await page.waitForLoadState('networkidle', { timeout: 15000 })
    } catch (error) {
      // Network idle timeout, continuing...
    }

    // Check for intermediate states (Welcome back text OR success checkmark)
    const hasWelcomeText = await page
      .locator('text=Welcome back!')
      .isVisible()
      .catch(() => false)
    const hasSuccessCheckmark = await page
      .locator('img[alt*="Success"], img[alt*="checkmark"]')
      .isVisible()
      .catch(() => false)

    if (hasWelcomeText || hasSuccessCheckmark) {
      await page
        .waitForLoadState('networkidle', { timeout: 15000 })
        .catch(() => {})

      // If still on intermediate page after waiting, navigate manually
      await page.waitForTimeout(3000) // Extended pause
      if (page.url().includes('/login') || !page.url().includes('/program')) {
        await page.goto('/program')
        await page
          .waitForLoadState('networkidle', { timeout: 15000 })
          .catch(() => {})
      }
    }

    // Now wait for navigation to /program with extended timeout and retry logic
    try {
      await page.waitForURL('/program', { timeout: 30000 })
    } catch (error) {
      // If still failing, try manual navigation one more time
      await page.goto('/program')
      await page.waitForURL('/program', { timeout: 15000 })
    }

    // Check if there's an active workout or start a new one
    const continueButton = page.getByRole('button', { name: /continue/i })
    const startButton = page.getByRole('button', { name: /open workout/i })

    if (await continueButton.isVisible({ timeout: 2000 }).catch(() => false)) {
      // Wait for button to be stable before clicking
      await continueButton.waitFor({ state: 'attached', timeout: 5000 })
      await page.waitForTimeout(1000) // Allow any animations to complete
      await continueButton.click({ force: true })
    } else {
      // Enhanced stability check for start button
      await startButton.waitFor({ state: 'attached', timeout: 5000 })
      await page.waitForTimeout(1000) // Allow any shimmer/hover effects to stabilize

      // Multiple click attempts with increasing delays
      let clicked = false
      for (let i = 0; i < 3 && !clicked; i++) {
        try {
          await startButton.click({ force: true, timeout: 5000 }) // eslint-disable-line no-await-in-loop
          clicked = true
        } catch (error) {
          if (i === 2) throw error
          // Click attempt failed, retrying after delay...
          await page.waitForTimeout((i + 1) * 1000) // eslint-disable-line no-await-in-loop
        }
      }
    }

    // Wait for navigation to workout page with extended timeout and retry logic
    try {
      await page.waitForURL(/\/workout/, { timeout: 25000 })
    } catch (error) {
      // If navigation fails, try manual navigation
      // Navigation timeout, attempting manual navigation...
      await page.goto('/workout')
      await page.waitForURL(/\/workout/, { timeout: 15000 })
    }
    await waitForLoadingComplete(page)

    // Monitor console for errors
    const errorMonitor = createConsoleErrorMonitor(page)

    // Wait for workout page to be fully loaded with exercise cards
    await page
      .waitForLoadState('networkidle', { timeout: 15000 })
      .catch(() => {})
    await page.waitForTimeout(3000) // Give extra time for dynamic content

    // Click on first exercise (use correct selector for ExerciseCard)
    const exerciseCards = page.locator('[data-testid="exercise-card"]')

    // Try alternative selectors if main selector fails
    const cardCount1 = await exerciseCards.count()
    if (cardCount1 === 0) {
      // Try alternative exercise card selectors
      const altCards = page.locator(
        'button[data-testid*="exercise"], [data-testid*="exercise-card"], button:has-text("Exercise:")'
      )
      if ((await altCards.count()) > 0) {
        // Using alternative exercise card selector
        await expect(altCards.first()).toBeVisible({ timeout: 10000 })
      } else {
        // Debug: show what's actually on the page
        // const pageContent = await page.content()
        // Page URL: ${page.url()}
        // Page contains data-testid: ${pageContent.includes('data-testid')}
        // Page contains exercise: ${pageContent.includes('exercise')}
        throw new Error('No exercise cards found on workout page')
      }
    } else {
      await expect(exerciseCards.first()).toBeVisible({ timeout: 10000 })
    }

    // Add diagnostic logging
    const cardCount2 = await exerciseCards.count()
    // Found ${cardCount2} exercise cards

    if (cardCount2 > 0) {
      // const firstCardText = await exerciseCards.first().textContent()
      // First card text: "${firstCardText}"
    }

    // Simulate rapid exercise tapping (click the first exercise button) with stability
    const firstCard = exerciseCards.first()
    await firstCard.waitFor({ state: 'attached', timeout: 10000 })

    // Use multiple attempts for exercise card click with DOM stability
    let cardClicked = false
    for (let i = 0; i < 3 && !cardClicked; i++) {
      try {
        await firstCard.click({ force: true, timeout: 5000 }) // eslint-disable-line no-await-in-loop
        cardClicked = true
      } catch (error) {
        if (i === 2) throw error
        await page.waitForTimeout(1000) // eslint-disable-line no-await-in-loop
      }
    }

    // Wait for navigation to exercise page with extended timeout
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 20000 })
    await waitForLoadingComplete(page)

    // Verify no race condition errors during rapid tapping
    expect(errorMonitor.getRaceConditionErrors()).toHaveLength(0)
  })

  test('Load recommendations flow - async loading without conflicts', async ({
    page,
  }) => {
    // Set longer timeout for stability
    test.setTimeout(90000)

    // First login and navigate to a workout
    await page.goto('/login')
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for page to fully load after login with extended timeout
    try {
      await page.waitForLoadState('networkidle', { timeout: 15000 })
    } catch (error) {
      // Network idle timeout, continuing...
    }

    // Check for intermediate states (Welcome back text OR success checkmark)
    const hasWelcomeText = await page
      .locator('text=Welcome back!')
      .isVisible()
      .catch(() => false)
    const hasSuccessCheckmark = await page
      .locator('img[alt*="Success"], img[alt*="checkmark"]')
      .isVisible()
      .catch(() => false)

    if (hasWelcomeText || hasSuccessCheckmark) {
      await page
        .waitForLoadState('networkidle', { timeout: 15000 })
        .catch(() => {})

      // If still on intermediate page after waiting, navigate manually
      await page.waitForTimeout(3000) // Extended pause
      if (page.url().includes('/login') || !page.url().includes('/program')) {
        await page.goto('/program')
        await page
          .waitForLoadState('networkidle', { timeout: 15000 })
          .catch(() => {})
      }
    }

    // Now wait for navigation to /program with extended timeout and retry logic
    try {
      await page.waitForURL('/program', { timeout: 30000 })
    } catch (error) {
      // If still failing, try manual navigation one more time
      await page.goto('/program')
      await page.waitForURL('/program', { timeout: 15000 })
    }

    // Check if there's an active workout or start a new one
    const continueButton = page.getByRole('button', { name: /continue/i })
    const startButton = page.getByRole('button', { name: /open workout/i })

    if (await continueButton.isVisible({ timeout: 2000 }).catch(() => false)) {
      await continueButton.click({ force: true })
    } else {
      await startButton.click({ force: true })
    }

    // Wait for navigation to workout page with extended timeout and retry logic
    try {
      await page.waitForURL(/\/workout/, { timeout: 20000 })
    } catch (error) {
      // If navigation fails, try manual navigation
      // Navigation timeout, attempting manual navigation...
      await page.goto('/workout')
      await page.waitForURL(/\/workout/, { timeout: 15000 })
    }
    await waitForLoadingComplete(page)

    // Click on first exercise (use correct selector for ExerciseCard)
    const exerciseCards = page.locator('[data-testid="exercise-card"]')
    await expect(exerciseCards.first()).toBeVisible({ timeout: 10000 })

    // Add diagnostic logging
    const cardCount3 = await exerciseCards.count()
    // Found ${cardCount3} exercise cards for recommendations test

    if (cardCount3 > 0) {
      // const firstCardText = await exerciseCards.first().textContent()
      // First card text: "${firstCardText}"
    }

    // Click exercise card with stability handling
    const firstCard = exerciseCards.first()
    await firstCard.waitFor({ state: 'attached', timeout: 10000 })

    // Use multiple attempts for exercise card click with DOM stability
    let cardClicked = false
    for (let i = 0; i < 3 && !cardClicked; i++) {
      try {
        await firstCard.click({ force: true, timeout: 5000 }) // eslint-disable-line no-await-in-loop
        cardClicked = true
      } catch (error) {
        if (i === 2) throw error
        await page.waitForTimeout(1000) // eslint-disable-line no-await-in-loop
      }
    }

    // Wait for navigation to exercise page with extended timeout
    await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 20000 })

    // Monitor console for errors
    const errorMonitor = createConsoleErrorMonitor(page)

    // Wait for exercise page to load completely
    await waitForLoadingComplete(page)

    // Verify we're on the exercise page
    expect(page.url()).toMatch(/\/workout\/exercise\/\d+/)

    // Simulate multiple refreshes to test async loading without race conditions
    for (let i = 0; i < 3; i++) {
      try {
        // eslint-disable-next-line no-await-in-loop
        await page.reload({ timeout: 15000 })
        // eslint-disable-next-line no-await-in-loop
        await waitForLoadingComplete(page)
        // eslint-disable-next-line no-await-in-loop
        await page.waitForURL(/\/workout\/exercise\/\d+/, { timeout: 20000 })
      } catch (error) {
        // Reload ${i + 1} failed, continuing...
        // If reload fails, skip remaining reloads to avoid browser closure
        break
      }
    }

    // Verify no race condition errors during async operations
    expect(errorMonitor.getRaceConditionErrors()).toHaveLength(0)
  })
})
